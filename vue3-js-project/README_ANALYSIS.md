# Excel数据分析系统 - 完整使用指南

## 🎯 项目概述
本项目是一个基于Vue 3的Excel数据分析系统，集成了DeepSeek AI进行智能数据分析，支持图表可视化、批量处理、历史记录和模板库等高级功能。

## 📦 技术栈
- **前端框架**: Vue 3 + Composition API
- **UI组件库**: Ant Design Vue
- **图表库**: ECharts 5.4.3
- **Excel处理**: SheetJS (XLSX)
- **AI集成**: DeepSeek API
- **构建工具**: Vite

## 🚀 功能特性

### 核心功能
- ✅ **真实AI分析** - 集成DeepSeek API
- ✅ **Excel文件处理** - 支持.xlsx/.xls格式
- ✅ **智能数据预览** - 实时显示数据结构
- ✅ **专业分析报告** - Markdown格式输出

### 扩展功能
- ✅ **图表可视化** - 4种图表类型（柱状图、折线图、饼图、散点图）
- ✅ **批量处理** - 最多5个文件同时分析
- ✅ **历史记录** - 本地存储50条分析记录
- ✅ **模板库** - 5个专业分析模板
- ✅ **拖拽上传** - 支持文件拖拽上传
- ✅ **响应式设计** - 完美适配移动端

## 📋 使用步骤

### 1. 环境准备
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 2. API配置
1. 访问 [DeepSeek官网](https://deepseek.com) 注册账号
2. 获取API密钥
3. 在界面中配置API密钥（安全存储在本地）

### 3. 基础使用
1. **上传文件**: 拖拽或点击上传Excel文件
2. **选择模板**: 从模板库选择或自定义分析需求
3. **开始分析**: 点击"开始分析"按钮
4. **查看结果**: 查看AI生成的专业分析报告
5. **下载报告**: 一键下载Markdown格式报告

### 4. 高级功能
- **图表可视化**: 点击"图表可视化"按钮查看数据图表
- **批量分析**: 点击"批量分析"处理多个文件
- **历史记录**: 查看过往分析记录
- **模板应用**: 快速使用预设分析模板

## 📊 分析模板说明

| 模板名称 | 适用场景 | 分析内容 |
|---------|----------|----------|
| 销售数据分析 | 销售报表、业绩分析 | 趋势分析、产品表现、客户行为 |
| 财务报表分析 | 财务数据、成本控制 | 收支结构、利润率、财务健康度 |
| 人力资源分析 | 员工数据、绩效评估 | 绩效分布、离职率、薪酬结构 |
| 库存管理分析 | 库存数据、采购优化 | 周转率、缺货风险、积压商品 |
| 客户行为分析 | 客户数据、营销分析 | 价值分层、留存率、生命周期 |

## 🔧 技术实现

### 文件结构
```
vue3-js-project/
├── src/
│   ├── views/
│   │   ├── AnalyseDocView.vue          # 基础版本
│   │   └── AnalyseDocViewEnhanced.vue  # 增强版本
│   ├── utils/
│   │   ├── excelGenerator.js
│   │   └── dateUtils.js
│   ├── composables/
│   │   ├── useItinerary.js
│   │   └── useExcelExport.js
│   └── assets/
│       └── main.css
├── package.json
└── README_ANALYSIS.md
```

### 关键配置
- **文件大小限制**: 10MB
- **批量处理限制**: 5个文件
- **历史记录限制**: 50条
- **API超时**: 30秒

## 🎨 界面特色
- **现代化UI**: Ant Design Vue组件库
- **响应式设计**: 完美适配各种屏幕尺寸
- **交互体验**: 拖拽上传、实时预览、进度显示
- **数据安全**: 本地处理，API密钥加密存储

## 📱 移动端支持
- 响应式布局设计
- 触摸友好的交互
- 优化的移动端体验

## 🔒 数据安全
- 所有文件本地处理，不上传服务器
- API密钥安全存储在浏览器本地存储
- 无敏感数据泄露风险

## 🚀 快速开始
1. 克隆项目
2. 安装依赖: `npm install`
3. 配置DeepSeek API密钥
4. 上传Excel文件开始分析

## 📞 技术支持
- 确保Node.js版本 >= 16
- 推荐使用最新版Chrome浏览器
- 如有问题请检查API密钥和网络连接
