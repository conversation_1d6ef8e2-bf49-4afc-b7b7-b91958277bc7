<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Legend点击测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div id="chart" style="width: 800px; height: 400px; margin: 20px;"></div>
    <div style="margin: 20px;">
        <button onclick="testLineChart()">测试折线图</button>
        <button onclick="testBarChart()">测试柱状图</button>
        <button onclick="testScatterChart()">测试散点图</button>
        <button onclick="testPieChart()">测试饼图</button>
    </div>

    <script>
        const chartDom = document.getElementById('chart');
        let myChart = echarts.init(chartDom);

        function testLineChart() {
            const option = {
                title: { text: '折线图测试' },
                tooltip: { trigger: 'axis' },
                legend: {
                    data: ['销售额', '利润', '数量'],
                    selected: { '销售额': true, '利润': true, '数量': true }
                },
                xAxis: {
                    type: 'category',
                    data: ['第1行', '第2行', '第3行', '第4行', '第5行']
                },
                yAxis: { type: 'value' },
                series: [
                    {
                        name: '销售额',
                        type: 'line',
                        data: [1000, 1500, 800, 2000, 1200],
                        smooth: true
                    },
                    {
                        name: '利润',
                        type: 'line',
                        data: [200, 300, 150, 400, 250],
                        smooth: true
                    },
                    {
                        name: '数量',
                        type: 'line',
                        data: [50, 75, 40, 100, 60],
                        smooth: true
                    }
                ]
            };
            
            myChart.setOption(option);
            addLegendHandlers();
        }

        function testBarChart() {
            const option = {
                title: { text: '柱状图测试' },
                tooltip: { trigger: 'axis' },
                legend: {
                    data: ['销售额', '利润', '数量'],
                    selected: { '销售额': true, '利润': true, '数量': true }
                },
                xAxis: {
                    type: 'category',
                    data: ['第1行', '第2行', '第3行', '第4行', '第5行']
                },
                yAxis: { type: 'value' },
                series: [
                    {
                        name: '销售额',
                        type: 'bar',
                        data: [1000, 1500, 800, 2000, 1200]
                    },
                    {
                        name: '利润',
                        type: 'bar',
                        data: [200, 300, 150, 400, 250]
                    },
                    {
                        name: '数量',
                        type: 'bar',
                        data: [50, 75, 40, 100, 60]
                    }
                ]
            };
            
            myChart.setOption(option);
            addLegendHandlers();
        }

        function testScatterChart() {
            const option = {
                title: { text: '散点图测试' },
                tooltip: { trigger: 'item' },
                legend: {
                    data: ['散点'],
                    selected: { '散点': true }
                },
                xAxis: { type: 'value', name: '销售额' },
                yAxis: { type: 'value', name: '利润' },
                series: [{
                    name: '散点',
                    type: 'scatter',
                    data: [[1000, 200], [1500, 300], [800, 150], [2000, 400], [1200, 250]],
                    symbolSize: 8
                }]
            };
            
            myChart.setOption(option);
            addLegendHandlers();
        }

        function testPieChart() {
            const option = {
                title: { text: '饼图测试' },
                tooltip: { trigger: 'item' },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ['第1行', '第2行', '第3行', '第4行', '第5行'],
                    selected: { '第1行': true, '第2行': true, '第3行': true, '第4行': true, '第5行': true }
                },
                series: [{
                    name: '销售额',
                    type: 'pie',
                    radius: '50%',
                    data: [
                        { name: '第1行', value: 1000 },
                        { name: '第2行', value: 1500 },
                        { name: '第3行', value: 800 },
                        { name: '第4行', value: 2000 },
                        { name: '第5行', value: 1200 }
                    ]
                }]
            };
            
            myChart.setOption(option);
            addLegendHandlers();
        }

        function addLegendHandlers() {
            // 清除之前的事件监听器
            myChart.off('legendselectchanged');
            myChart.off('legendselected');
            myChart.off('legendunselected');
            
            // 添加事件处理
            myChart.on('legendselectchanged', function(params) {
                try {
                    console.log('Legend selection changed:', params);
                    if (!params || !params.selected) {
                        console.warn('Invalid legend params:', params);
                        return;
                    }
                } catch (error) {
                    console.error('Legend select changed error:', error);
                }
            });

            myChart.on('legendselected', function(params) {
                try {
                    console.log('Legend selected:', params);
                } catch (error) {
                    console.error('Legend selected error:', error);
                }
            });

            myChart.on('legendunselected', function(params) {
                try {
                    console.log('Legend unselected:', params);
                } catch (error) {
                    console.error('Legend unselected error:', error);
                }
            });
        }

        // 默认显示折线图
        testLineChart();
    </script>
</body>
</html>
