import ChartView from '../../view/Chart.js';
import GlobalModel from '../../model/Global.js';
import ExtensionAPI from '../../core/ExtensionAPI.js';
import { StageHandlerProgressParams, Payload } from '../../util/types.js';
import BarSeriesModel from './BarSeries.js';
import Element from 'zrender/lib/Element.js';
declare class BarView extends ChartView {
    static type: "bar";
    type: "bar";
    private _data;
    private _isLargeDraw;
    private _isFirstFrame;
    private _onRendered;
    private _backgroundGroup;
    private _backgroundEls;
    private _model;
    private _progressiveEls;
    constructor();
    render(seriesModel: BarSeriesModel, ecModel: GlobalModel, api: ExtensionAPI, payload: Payload): void;
    incrementalPrepareRender(seriesModel: BarSeriesModel): void;
    incrementalRender(params: StageHandlerProgressParams, seriesModel: BarSeriesModel): void;
    eachRendered(cb: (el: Element) => boolean | void): void;
    private _updateDrawMode;
    private _renderNormal;
    private _renderLarge;
    private _incrementalRenderLarge;
    private _updateLargeClip;
    private _enableRealtimeSort;
    private _dataSort;
    private _isOrderChangedWithinSameData;
    private _isOrderDifferentInView;
    private _updateSortWithinSameData;
    private _dispatchInitSort;
    remove(ecModel: GlobalModel, api: ExtensionAPI): void;
    dispose(ecModel: GlobalModel, api: ExtensionAPI): void;
    private _removeOnRenderedListener;
    private _clear;
    private _removeBackground;
}
export default BarView;
