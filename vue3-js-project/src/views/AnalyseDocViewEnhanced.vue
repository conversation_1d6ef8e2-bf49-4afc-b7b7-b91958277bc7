<template>
  <div class="analyse-doc-container">
    <a-card title="Excel数据分析增强版" class="analyse-card">
      <!-- 功能导航栏 -->
      <a-space class="feature-nav" style="margin-bottom: 20px">
        <a-button type="primary" @click="showHistory = !showHistory">
          <history-outlined />
          历史记录
        </a-button>
        <a-button type="primary" @click="showTemplates = !showTemplates">
          <folder-outlined />
          分析模板
        </a-button>
        <a-button type="primary" @click="showCharts = !showCharts">
          <bar-chart-outlined />
          图表可视化
        </a-button>
        <a-button type="primary" @click="showBatch = !showBatch">
          <upload-outlined />
          批量分析
        </a-button>
      </a-space>

      <a-row :gutter="24">
        <!-- 左侧主面板 -->
        <a-col :span="showCharts ? 16 : 24">
          <a-form layout="vertical">
            <!-- 文件上传区域 -->
            <a-form-item label="上传Excel文件">
              <a-upload-dragger
                v-model:fileList="fileList"
                :multiple="enableBatchUpload"
                :before-upload="beforeUpload"
                :remove="handleRemove"
                accept=".xlsx,.xls"
                :max-count="enableBatchUpload ? 5 : 1"
              >
                <p class="ant-upload-drag-icon">
                  <inbox-outlined />
                </p>
                <p class="ant-upload-text">点击或拖拽文件到此处上传</p>
                <p class="ant-upload-hint">
                  支持单个或批量上传Excel文件，每个文件不超过10MB
                </p>
              </a-upload-dragger>
            </a-form-item>

            <!-- 分析模板选择 -->
            <a-form-item label="选择分析模板" v-if="showTemplates">
              <a-select v-model:value="selectedTemplate" style="width: 100%" @change="applyTemplate">
                <a-select-option value="">自定义分析</a-select-option>
                <a-select-option value="sales">销售数据分析</a-select-option>
                <a-select-option value="finance">财务报表分析</a-select-option>
                <a-select-option value="hr">人力资源分析</a-select-option>
                <a-select-option value="inventory">库存管理分析</a-select-option>
                <a-select-option value="customer">客户行为分析</a-select-option>
              </a-select>
            </a-form-item>

            <!-- 分析类型和配置 -->
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="分析类型">
                  <a-select v-model:value="analysisType" style="width: 100%">
                    <a-select-option value="general">通用分析</a-select-option>
                    <a-select-option value="trend">趋势分析</a-select-option>
                    <a-select-option value="statistics">统计分析</a-select-option>
                    <a-select-option value="correlation">相关性分析</a-select-option>
                    <a-select-option value="anomaly">异常检测</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="图表类型">
                  <a-select v-model:value="chartType" style="width: 100%">
                    <a-select-option value="bar">柱状图</a-select-option>
                    <a-select-option value="line">折线图</a-select-option>
                    <a-select-option value="pie">饼图</a-select-option>
                    <a-select-option value="scatter">散点图</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- API配置 -->
            <a-form-item>
              <a-button type="link" @click="showSettings = !showSettings">
                <setting-outlined />
                API配置
              </a-button>
            </a-form-item>

            <a-collapse v-if="showSettings" v-model:activeKey="settingsActiveKey">
              <a-collapse-panel key="1" header="DeepSeek API配置">
                <a-form-item label="API密钥">
                  <a-input-password
                    v-model:value="apiKey"
                    placeholder="请输入DeepSeek API密钥"
                    :max-length="100"
                  />
                </a-form-item>
                <a-alert
                  message="您的API密钥将安全地存储在浏览器本地存储中"
                  type="info"
                  show-icon
                />
              </a-collapse-panel>
            </a-collapse>

            <!-- 分析需求输入 -->
            <a-form-item label="分析需求">
              <a-textarea
                v-model:value="analysisRequirement"
                :placeholder="getTemplatePlaceholder()"
                :rows="4"
                :max-length="1000"
                show-count
              />
            </a-form-item>

            <!-- 数据预览 -->
            <div v-if="excelData" class="data-preview">
              <a-divider>数据预览</a-divider>
              <a-descriptions :column="3" size="small" bordered>
                <a-descriptions-item label="工作表">{{ excelData.sheetName }}</a-descriptions-item>
                <a-descriptions-item label="列数">{{ excelData.headers.length }}</a-descriptions-item>
                <a-descriptions-item label="行数">{{ excelData.totalRows }}</a-descriptions-item>
              </a-descriptions>
              
              <a-table
                v-if="excelData.headers.length > 0"
                :dataSource="previewData"
                :columns="previewColumns"
                :pagination="{ pageSize: 5 }"
                size="small"
                style="margin-top: 16px"
                :scroll="{ x: 'max-content' }"
              />
            </div>

            <!-- 进度和错误 -->
            <a-progress
              v-if="isAnalyzing"
              :percent="progress"
              :status="progress === 100 ? 'success' : 'normal'"
            />
            <a-alert
              v-if="errorMessage"
              :message="errorMessage"
              type="error"
              show-icon
              closable
              @close="errorMessage = ''"
            />

            <!-- 提交按钮 -->
            <a-form-item>
              <a-button
                type="primary"
                :loading="loading || isAnalyzing"
                :disabled="!canSubmit"
                @click="handleSubmit"
                size="large"
                block
              >
                <template #icon>
                  <play-circle-outlined v-if="!loading && !isAnalyzing" />
                  <loading-outlined v-else />
                </template>
                {{ isAnalyzing ? '分析中...' : '开始分析' }}
              </a-button>
            </a-form-item>
          </a-form>
        </a-col>

        <!-- 右侧图表面板 -->
        <a-col :span="8" v-if="showCharts">
          <a-card title="数据可视化" size="small">
            <div id="chart-container" style="height: 400px; width: 100%;"></div>
            <a-empty v-if="!chartData" description="请先上传数据并选择图表类型" />
          </a-card>
        </a-col>
      </a-row>

      <!-- 历史记录抽屉 -->
      <a-drawer
        v-model:open="showHistory"
        title="分析历史记录"
        placement="right"
        :width="400"
      >
            <a-list
              v-if="analysisHistory.length > 0"
              :dataSource="analysisHistory"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.fileName"
                    :description="`${item.type} - ${item.date}`"
                  />
                  <template #actions>
                    <a-button type="link" size="small">查看</a-button>
                  </template>
                </a-list-item>
              </template>
            </a-list>
        <a-empty v-else description="暂无历史记录" />
      </a-drawer>

      <!-- 模板库抽屉 -->
      <a-drawer
        v-model:open="showTemplates"
        title="分析模板库"
        placement="right"
        :width="400"
      >
        <a-list :dataSource="analysisTemplates">
          <template #renderItem="{ item }">
            <a-list-item @click="selectTemplate(item.key)" style="cursor: pointer;">
              <a-list-item-meta
                :title="item.name"
                :description="item.description"
              />
            </a-list-item>
          </template>
        </a-list>
      </a-drawer>

      <!-- 批量分析抽屉 -->
      <a-drawer
        v-model:open="showBatch"
        title="批量分析"
        placement="right"
        :width="600"
      >
        <a-space direction="vertical" style="width: 100%">
          <a-alert
            message="批量分析说明"
            description="支持同时分析多个Excel文件，每个文件将独立进行分析并生成报告。分析完成后可下载汇总报告或单独查看每个文件的分析结果。"
            type="info"
            show-icon
          />
          
          <a-upload
            :file-list="batchFiles"
            :before-upload="handleBatchUpload"
            :remove="handleBatchRemove"
            accept=".xlsx,.xls"
            multiple
            :max-count="10"
          >
            <a-button>
              <upload-outlined />
              选择多个文件
            </a-button>
          </a-upload>

          <!-- 批量分析配置 -->
          <a-card size="small" title="分析配置">
            <a-form layout="vertical">
              <a-form-item label="分析模板">
                <a-select v-model:value="batchTemplate" style="width: 100%">
                  <a-select-option value="">自定义分析</a-select-option>
                  <a-select-option value="sales">销售数据分析</a-select-option>
                  <a-select-option value="finance">财务报表分析</a-select-option>
                  <a-select-option value="hr">人力资源分析</a-select-option>
                  <a-select-option value="inventory">库存管理分析</a-select-option>
                  <a-select-option value="customer">客户行为分析</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="分析需求">
                <a-textarea
                  v-model:value="batchAnalysisRequirement"
                  :placeholder="getBatchTemplatePlaceholder()"
                  :rows="3"
                  :max-length="500"
                  show-count
                />
              </a-form-item>
              
              <a-form-item label="输出格式">
                <a-radio-group v-model:value="batchOutputFormat">
                  <a-radio value="individual">单独报告</a-radio>
                  <a-radio value="combined">汇总报告</a-radio>
                  <a-radio value="both">两者都生成</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 批量分析进度 -->
          <div v-if="batchAnalyzing">
            <a-progress
              :percent="batchProgress"
              :status="batchProgress === 100 ? 'success' : 'normal'"
            />
            <div style="margin-top: 8px">
              <a-textarea
                :value="batchLog"
                :rows="4"
                readonly
                placeholder="分析日志..."
              />
            </div>
          </div>

          <!-- 批量分析结果预览 -->
          <div v-if="batchResults.length > 0">
            <a-divider>分析结果</a-divider>
            <a-list
              :dataSource="batchResults"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.fileName"
                    :description="`状态: ${item.status} | 用时: ${item.duration}ms`"
                  />
                  <template #actions>
                    <a-button 
                      v-if="item.result" 
                      type="link" 
                      size="small"
                      @click="viewBatchResult(item)"
                    >
                      查看
                    </a-button>
                    <a-button 
                      v-if="item.error" 
                      type="link" 
                      size="small"
                      @click="showError(item.error)"
                    >
                      错误详情
                    </a-button>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </div>

          <a-button
            type="primary"
            :disabled="batchFiles.length === 0 || batchAnalyzing"
            :loading="batchAnalyzing"
            @click="startBatchAnalysis"
            block
          >
            {{ batchAnalyzing ? '分析中...' : '开始批量分析' }}
          </a-button>

          <a-space v-if="batchResults.length > 0" style="width: 100%">
            <a-button @click="downloadBatchReport" block>
              <download-outlined />
              下载汇总报告
            </a-button>
            <a-button @click="clearBatchResults" block>
              清空结果
            </a-button>
          </a-space>
        </a-space>
      </a-drawer>

      <!-- 分析结果 -->
      <div v-if="analysisResult" class="result-section">
        <a-divider />
        <div class="result-header">
          <h3>分析结果</h3>
          <a-space>
            <a-button @click="downloadResult" type="default" size="small">
              <download-outlined />
              下载报告
            </a-button>
            <a-button @click="saveToHistory" type="primary" size="small">
              保存到历史
            </a-button>
          </a-space>
        </div>
        <div class="markdown-content" v-html="renderedMarkdown"></div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick,markRaw } from 'vue'
import { message } from 'ant-design-vue'
import { UploadOutlined, DownloadOutlined, SettingOutlined, PlayCircleOutlined, LoadingOutlined, HistoryOutlined, BarChartOutlined, FolderOutlined, InboxOutlined } from '@ant-design/icons-vue'
import { marked } from 'marked'
import * as XLSX from 'xlsx'
import * as echarts from 'echarts'

// 状态管理
const fileList = ref([])
const analysisRequirement = ref('')
const loading = ref(false)
const analysisResult = ref('')
const excelData = ref(null)
const analysisType = ref('general')
const apiKey = ref('')
const showSettings = ref(false)
const showHistory = ref(false)
const showTemplates = ref(false)
const showCharts = ref(false)
const showBatch = ref(false)
const enableBatchUpload = ref(false)
const errorMessage = ref('')
const isAnalyzing = ref(false)
const progress = ref(0)
const settingsActiveKey = ref(['1'])
const batchFiles = ref([])
const analysisHistory = ref([])
const selectedTemplate = ref('')
const chartType = ref('bar')
const chartInstance = ref(null)
const chartData = ref(null)

// 批量分析状态
const batchTemplate = ref('')
const batchAnalysisRequirement = ref('')
const batchOutputFormat = ref('individual')
const batchAnalyzing = ref(false)
const batchProgress = ref(0)
const batchLog = ref('')
const batchResults = ref([])

// 分析模板
const analysisTemplates = [
  {
    key: 'sales',
    name: '销售数据分析',
    description: '分析销售趋势、产品表现和客户行为',
    prompt: '请分析销售数据，包括：1) 月度销售趋势 2) 产品类别表现 3) 客户购买行为 4) 销售预测建议'
  },
  {
    key: 'finance',
    name: '财务报表分析',
    description: '分析收入、支出、利润和现金流',
    prompt: '请分析财务数据，包括：1) 收入支出结构 2) 利润率变化 3) 成本控制 4) 财务健康度评估'
  },
  {
    key: 'hr',
    name: '人力资源分析',
    description: '分析员工绩效、离职率和薪酬结构',
    prompt: '请分析人力资源数据，包括：1) 员工绩效分布 2) 离职率分析 3) 薪酬结构合理性 4) 人才发展建议'
  },
  {
    key: 'inventory',
    name: '库存管理分析',
    description: '分析库存周转、缺货和积压情况',
    prompt: '请分析库存数据，包括：1) 库存周转率 2) 缺货风险 3) 积压商品 4) 采购优化建议'
  },
  {
    key: 'customer',
    name: '客户行为分析',
    description: '分析客户价值、留存率和满意度',
    prompt: '请分析客户数据，包括：1) 客户价值分层 2) 留存率分析 3) 满意度评估 4) 客户生命周期管理'
  }
]

// 计算属性
const canSubmit = computed(() => {
  return fileList.value.length > 0 && analysisRequirement.value.trim() && !loading.value && !isAnalyzing.value
})

const renderedMarkdown = computed(() => {
  if (!analysisResult.value) return ''
  return marked(analysisResult.value)
})

const previewData = computed(() => {
  if (!excelData.value) return []
  return excelData.value.rows.slice(0, 5).map((row, index) => {
    const obj = { key: index }
    excelData.value.headers.forEach((header, i) => {
      obj[header] = row[i] || ''
    })
    return obj
  })
})

const previewColumns = computed(() => {
  if (!excelData.value) return []
  return excelData.value.headers.map(header => ({
    title: header,
    dataIndex: header,
    key: header,
    width: 150,
    ellipsis: true
  }))
})

// 生命周期
onMounted(() => {
  // 从本地存储加载数据
  const savedApiKey = localStorage.getItem('deepseek_api_key')
  const savedHistory = localStorage.getItem('analysis_history')
  
  if (savedApiKey) apiKey.value = savedApiKey
  if (savedHistory) analysisHistory.value = JSON.parse(savedHistory)
})

// 监听变化
watch(apiKey, (newKey) => {
  if (newKey) localStorage.setItem('deepseek_api_key', newKey)
  else localStorage.removeItem('deepseek_api_key')
})

watch(analysisHistory, (newHistory) => {
  localStorage.setItem('analysis_history', JSON.stringify(newHistory))
}, { deep: true })

// 文件上传处理
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  
  if (!isExcel) {
    message.error('只能上传Excel文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  
  if (enableBatchUpload.value) {
    fileList.value = [...fileList.value, file]
  } else {
    fileList.value = [file]
  }
  
  readExcelFile(file)
  return false
}

const handleRemove = (file) => {
  const index = fileList.value.indexOf(file)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
  if (fileList.value.length === 0) {
    excelData.value = null
  }
}

// 批量处理
const handleBatchUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  
  if (!isExcel) {
    message.error('只能上传Excel文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  
  batchFiles.value = [...batchFiles.value, file]
  return false
}

const handleBatchRemove = (file) => {
  const index = batchFiles.value.indexOf(file)
  if (index > -1) {
    batchFiles.value.splice(index, 1)
  }
}

const startBatchAnalysis = async () => {
  if (batchFiles.value.length === 0) {
    message.warning('请先选择文件')
    return
  }

  if (!apiKey.value.trim()) {
    message.warning('请先配置DeepSeek API密钥')
    showSettings.value = true
    return
  }

  batchAnalyzing.value = true
  batchProgress.value = 0
  batchLog.value = ''
  batchResults.value = []

  const startTime = Date.now()
  const totalFiles = batchFiles.value.length
  let completedFiles = 0

  try {
    // 清空之前的日志
    batchLog.value = `开始批量分析，共 ${totalFiles} 个文件...\n`

    // 逐个处理文件
    for (const file of batchFiles.value) {
      const fileStartTime = Date.now()
      batchLog.value += `正在分析: ${file.name}...\n`

      try {
        const result = await analyzeSingleFile(file)
        const duration = Date.now() - fileStartTime
        
        batchResults.value.push({
          fileName: file.name,
          status: '成功',
          result: result,
          duration: duration,
          error: null
        })
        
        batchLog.value += `✅ ${file.name} 分析完成 (${duration}ms)\n`
      } catch (error) {
        const duration = Date.now() - fileStartTime
        
        batchResults.value.push({
          fileName: file.name,
          status: '失败',
          result: null,
          duration: duration,
          error: error.message
        })
        
        batchLog.value += `❌ ${file.name} 分析失败: ${error.message}\n`
      }

      completedFiles++
      batchProgress.value = Math.round((completedFiles / totalFiles) * 100)
    }

    const totalDuration = Date.now() - startTime
    batchLog.value += `\n批量分析完成！总用时: ${totalDuration}ms`
    
    message.success(`批量分析完成！共处理 ${totalFiles} 个文件`)
    
    // 根据输出格式处理结果
    if (batchOutputFormat.value === 'combined' || batchOutputFormat.value === 'both') {
      generateCombinedReport()
    }

  } catch (error) {
    message.error('批量分析失败: ' + error.message)
    batchLog.value += `\n批量分析失败: ${error.message}`
  } finally {
    batchAnalyzing.value = false
  }
}

// 读取Excel文件
const readExcelFile = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      
      excelData.value = {
        sheetName: firstSheetName,
        headers: jsonData[0] || [],
        rows: jsonData.slice(1),
        totalRows: jsonData.length - 1
      }
      
      // 初始化图表数据
      initChartData()
      
      message.success('文件读取成功!')
    } catch (error) {
      message.error('文件读取失败: ' + error.message)
      console.error('Excel读取错误:', error)
    }
  }
  reader.readAsArrayBuffer(file)
}

// 模板应用
const selectTemplate = (templateKey) => {
  const template = analysisTemplates.find(t => t.key === templateKey)
  if (template) {
    selectedTemplate.value = templateKey
    analysisRequirement.value = template.prompt
    analysisType.value = templateKey === 'trend' ? 'trend' : 
                        templateKey === 'statistics' ? 'statistics' : 'general'
  }
}

const getTemplatePlaceholder = () => {
  if (selectedTemplate.value) {
    const template = analysisTemplates.find(t => t.key === selectedTemplate.value)
    return template ? template.prompt : '请输入您的分析需求...'
  }
  return '请输入您的分析需求，例如：分析销售数据的趋势、统计各产品的销售额等'
}

const getBatchTemplatePlaceholder = () => {
  if (batchTemplate.value) {
    const template = analysisTemplates.find(t => t.key === batchTemplate.value)
    return template ? template.prompt : '请输入批量分析需求...'
  }
  return '请输入批量分析需求，例如：分析所有文件的销售趋势、统计各产品的销售额等'
}

// 批量分析相关方法
const startBatchAnalysis = async () => {
  if (batchFiles.value.length === 0) {
    message.warning('请先选择文件')
    return
  }

  if (!apiKey.value.trim()) {
    message.warning('请先配置DeepSeek API密钥')
    showSettings.value = true
    return
  }

  batchAnalyzing.value = true
  batchProgress.value = 0
  batchLog.value = ''
  batchResults.value = []

  const startTime = Date.now()
  const totalFiles = batchFiles.value.length
  let completedFiles = 0

  try {
    // 清空之前的日志
    batchLog.value = `开始批量分析，共 ${totalFiles} 个文件...\n`

    // 逐个处理文件
    for (const file of batchFiles.value) {
      const fileStartTime = Date.now()
      batchLog.value += `正在分析: ${file.name}...\n`

      try {
        const result = await analyzeSingleFile(file)
        const duration = Date.now() - fileStartTime
        
        batchResults.value.push({
          fileName: file.name,
          status: '成功',
          result: result,
          duration: duration,
          error: null
        })
        
        batchLog.value += `✅ ${file.name} 分析完成 (${duration}ms)\n`
      } catch (error) {
        const duration = Date.now() - fileStartTime
        
        batchResults.value.push({
          fileName: file.name,
          status: '失败',
          result: null,
          duration: duration,
          error: error.message
        })
        
        batchLog.value += `❌ ${file.name} 分析失败: ${error.message}\n`
      }

      completedFiles++
      batchProgress.value = Math.round((completedFiles / totalFiles) * 100)
    }

    const totalDuration = Date.now() - startTime
    batchLog.value += `\n批量分析完成！总用时: ${totalDuration}ms`
    
    message.success(`批量分析完成！共处理 ${totalFiles} 个文件`)
    
    // 根据输出格式处理结果
    if (batchOutputFormat.value === 'combined' || batchOutputFormat.value === 'both') {
      generateCombinedReport()
    }

  } catch (error) {
    message.error('批量分析失败: ' + error.message)
    batchLog.value += `\n批量分析失败: ${error.message}`
  } finally {
    batchAnalyzing.value = false
  }
}

const analyzeSingleFile = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        const excelData = {
          sheetName: firstSheetName,
          headers: jsonData[0] || [],
          rows: jsonData.slice(1),
          totalRows: jsonData.length - 1
        }

        if (excelData.totalRows === 0) {
          throw new Error('文件中没有数据')
        }

        // 使用批量分析模板或自定义需求
        const requirement = batchAnalysisRequirement.value || 
          (batchTemplate.value ? getBatchTemplatePlaceholder() : '请分析此Excel数据')

        const result = await callBatchDeepSeekAPI(excelData, requirement)
        resolve(result)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsArrayBuffer(file)
  })
}

const callBatchDeepSeekAPI = async (data, requirement) => {
  const dataSummary = {
    sheetName: data.sheetName,
    headers: data.headers,
    totalRows: data.totalRows,
    sampleData: data.rows.slice(0, 15).map(row => 
      data.headers.reduce((obj, header, index) => {
        obj[header] = row[index] || null
        return obj
      }, {})
    )
  }

  const prompt = `请作为数据分析师，分析以下Excel数据，根据用户需求提供专业分析报告。

## 分析要求
- 用户需求：${requirement}

## 数据结构
- 工作表：${dataSummary.sheetName}
- 列数：${dataSummary.headers.length}
- 行数：${dataSummary.totalRows}
- 列名：${dataSummary.headers.join(' | ')}

## 数据样本（前15行）
\`\`\`json
${JSON.stringify(dataSummary.sampleData, null, 2)}
\`\`\`

## 分析任务
请提供以下内容的详细分析：
1. 数据概览和基本统计信息
2. 针对用户具体需求的深度分析
3. 关键业务洞察和数据模式
4. 数据质量评估（缺失值、异常值等）
5. 基于分析结果的具体行动建议

请使用清晰的Markdown格式，包含表格、列表和适当的格式化。`

  const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey.value.trim()}`
    },
    body: JSON.stringify({
      model: 'deepseek-chat',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的数据分析师，擅长Excel数据分析和商业洞察。请提供准确、实用的分析报告。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.7,
      stream: false
    })
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(`API调用失败: ${errorData.error?.message || response.statusText}`)
  }

  const result = await response.json()
  return result.choices[0]?.message?.content || '未能获取分析结果'
}

const generateCombinedReport = () => {
  const successfulResults = batchResults.value.filter(r => r.status === '成功')
  
  if (successfulResults.length === 0) return

  let combinedReport = `# 批量分析报告汇总\n\n`
  combinedReport += `**分析时间**: ${new Date().toLocaleString('zh-CN')}\n`
  combinedReport += `**总文件数**: ${batchFiles.value.length}\n`
  combinedReport += `**成功分析**: ${successfulResults.length}\n`
  combinedReport += `**失败文件**: ${batchFiles.value.length - successfulResults.length}\n\n`

  combinedReport += `## 分析结果概览\n\n`
  
  successfulResults.forEach((result, index) => {
    combinedReport += `### ${index + 1}. ${result.fileName}\n`
    combinedReport += `**分析用时**: ${result.duration}ms\n\n`
    combinedReport += `${result.result}\n\n`
    combinedReport += `---\n\n`
  })

  // 保存汇总报告到第一个成功的结果中
  if (successfulResults.length > 0) {
    batchResults.value.unshift({
      fileName: '汇总报告',
      status: '汇总',
      result: combinedReport,
      duration: 0,
      error: null
    })
  }
}

const viewBatchResult = (item) => {
  if (!item.result) return
  
  // 创建模态框显示结果
  const modal = document.createElement('div')
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  `
  
  const content = document.createElement('div')
  content.style.cssText = `
    background: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  `
  
  content.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
      <h3>${item.fileName} - 分析结果</h3>
      <button onclick="this.closest('div').parentElement.remove()" style="background: none; border: none; font-size: 20px; cursor: pointer;">×</button>
    </div>
    <div style="border: 1px solid #e8e8e8; border-radius: 4px; padding: 16px;">
      ${marked(item.result)}
    </div>
  `
  
  modal.appendChild(content)
  document.body.appendChild(modal)
  
  // 点击背景关闭
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.remove()
    }
  })
}

const showError = (error) => {
  message.error(`错误详情: ${error}`)
}

const downloadBatchReport = () => {
  if (batchResults.value.length === 0) {
    message.warning('暂无分析结果')
    return
  }

  let reportContent = ''
  
  if (batchOutputFormat.value === 'combined') {
    const combinedReport = batchResults.value.find(r => r.fileName === '汇总报告')
    if (combinedReport) {
      reportContent = combinedReport.result
    } else {
      generateCombinedReport()
      const combinedReport = batchResults.value.find(r => r.fileName === '汇总报告')
      reportContent = combinedReport ? combinedReport.result : ''
    }
  } else {
    // 生成包含所有单独报告的文件
    reportContent = `# 批量分析报告合集\n\n`
    reportContent += `**分析时间**: ${new Date().toLocaleString('zh-CN')}\n`
    reportContent += `**总文件数**: ${batchFiles.value.length}\n\n`
    
    batchResults.value.filter(r => r.fileName !== '汇总报告').forEach((result, index) => {
      reportContent += `## ${index + 1}. ${result.fileName}\n\n`
      if (result.status === '成功') {
        reportContent += result.result
      } else {
        reportContent += `**分析失败**: ${result.error}\n`
      }
      reportContent += `\n---\n\n`
    })
  }

  const blob = new Blob([reportContent], { type: 'text/markdown' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `batch-analysis-${new Date().getTime()}.md`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  
  message.success('批量报告下载成功!')
}

const clearBatchResults = () => {
  batchResults.value = []
  batchLog.value = ''
  batchProgress.value = 0
}

// 智能图表数据检测
const initChartData = () => {
  if (!excelData.value) return
  
  // 智能检测数值列 - 自动识别数字类型
  const numericColumns = excelData.value.headers.filter((header, index) => {
    // 检查前5行数据，如果大部分是数字则认为是数值列
    const sampleValues = excelData.value.rows.slice(0, 5).map(row => {
      const value = row[index]
      return value !== null && value !== undefined && value !== '' ? String(value) : ''
    })
    
    const numericCount = sampleValues.filter(val => {
      const num = parseFloat(val)
      return !isNaN(num) && isFinite(num)
    }).length
    
    return numericCount >= 3 // 至少3个有效数值
  })
  
  if (numericColumns.length > 0) {
    chartData.value = {
      columns: numericColumns,
      data: excelData.value.rows.slice(0, 20).map((row, index) => {
        const item = { name: `第${index + 1}行` }
        numericColumns.forEach(col => {
          const colIndex = excelData.value.headers.indexOf(col)
          const value = row[colIndex]
          item[col] = value !== null && value !== undefined && value !== '' ? 
            (isNaN(parseFloat(value)) ? 0 : parseFloat(value)) : 0
        })
        return item
      })
    }
    
    nextTick(() => {
      renderChart()
    })
  } else {
    chartData.value = null
  }
}

const renderChart = () => {
  if (!chartData.value || !showCharts.value) return

  const container = document.querySelector('#chart-container')
  if (!container) return

  try {
    // 清理之前的图表实例
    if (chartInstance.value) {
      chartInstance.value.dispose()
      chartInstance.value = null
    }

    // 初始化新的图表实例
    chartInstance.value = markRaw(echarts.init(container))
    // 确保图表数据有效
    if (!chartData.value.columns || chartData.value.columns.length === 0) {
      console.warn('No valid chart data columns found')
      return
    }

    if (!chartData.value.data || chartData.value.data.length === 0) {
      console.warn('No valid chart data found')
      return
    }

    // 过滤并标准化列名，确保legend数据格式正确
    chartData.value.columns = chartData.value.columns
      .filter(col => col != null && col !== undefined && col !== '')
      .map(col => String(col).trim())
      .filter(col => col.length > 0) // 确保trim后不为空
    console.log('Processed columns:', chartData.value.columns)
  } catch (error) {
    console.error('Chart initialization error:', error)
    message.error('图表初始化失败: ' + error.message)
    return
  }
  
  // 根据图表类型生成不同的配置
  let option = {}
  
  if (chartType.value === 'pie') {
    // 饼图配置 - 使用第一列数值列的数据
    const firstCol = chartData.value.columns[0]
    const pieData = chartData.value.data.map((item, index) => {
      const value = item && item[firstCol] !== undefined ? item[firstCol] : 0
      const itemName = `第${index + 1}行`
      return {
        name: String(itemName).trim(),
        value: typeof value === 'number' ? value : (parseFloat(value) || 0)
      }
    })
    
    option = {
      title: {
        text: `${firstCol} - 饼图`
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: pieData.map(item => String(item.name).trim()),
        selected: pieData.reduce((acc, item) => {
          const itemName = String(item.name).trim()
          acc[itemName] = true
          return acc
        }, {}) // 防止legend点击报错，初始化所有项为选中状态
      },
      series: [
        {
          name: firstCol,
          type: 'pie',
          radius: '50%',
          data: pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  } else if (chartType.value === 'scatter') {
    // 散点图配置 - 使用前两个数值列
    const col1 = chartData.value.columns[0]
    const col2 = chartData.value.columns[1] || chartData.value.columns[0]

    option = {
      title: {
        text: `${col1} vs ${col2} - 散点图`
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          return `${params.data[0]}, ${params.data[1]}`
        }
      },
      legend: {
        data: ['散点'],
        selected: { '散点': true } // 防止legend点击报错
      },
      xAxis: {
        type: 'value',
        name: col1
      },
      yAxis: {
        type: 'value',
        name: col2
      },
      series: [
        {
          name: '散点',
          type: 'scatter',
          data: chartData.value.data.map(item => {
            const val1 = item && item[col1] !== undefined ? item[col1] : 0
            const val2 = item && item[col2] !== undefined ? item[col2] : 0
            return [
              typeof val1 === 'number' ? val1 : (parseFloat(val1) || 0),
              typeof val2 === 'number' ? val2 : (parseFloat(val2) || 0)
            ]
          }),
          symbolSize: 8
        }
      ]
    }
  } else {
    // 柱状图和折线图配置
    option = {
    
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: chartData.value.columns ? chartData.value.columns.reduce((acc, col) => {
          acc.push(col)
          return acc
        }, []) : [],
        selected: chartData.value.columns ? chartData.value.columns.reduce((acc, col) => {
          acc[col] = true
          return acc
        }, {}) : {} // 防止legend点击报错，初始化所有项为选中状态
      },
      xAxis: {
        type: 'category',
        data: chartData.value.data.map((_, index) => `第${index + 1}行`)
      },
      yAxis: {
        type: 'value'
      },
      series: chartData.value.columns.map(col => ({
        name: col,
        type: chartType.value,
        data: chartData.value.data.map(item => {
          const value = item && item[col] !== undefined ? item[col] : 0
          return typeof value === 'number' ? value : (parseFloat(value) || 0)
        }),
        smooth: chartType.value === 'line'
      }))
    }
  }
  console.log('option',option)
  console.log('chartData.value.columns:', chartData.value.columns)
  console.log('legend data:', option.legend?.data)

  // 设置图表配置
  try {
    chartInstance.value.setOption(option)

    // 添加legend事件处理，防止报错
    chartInstance.value.on('legendselectchanged', function(params) {
      try {
        // 验证参数有效性
        if (!params || !params.selected) {
          console.warn('Invalid legend params:', params)
          return
        }

        // 处理legend选择变化事件
        console.log('Legend selection changed:', params)

        // 确保图表实例和选项仍然有效
        if (!chartInstance.value) {
          console.warn('Chart instance is null')
          return
        }

        // 这里可以添加自定义的legend处理逻辑
        // 例如：更新图表显示状态等

      } catch (error) {
        console.warn('Legend select changed error:', error)
      }
    })

    // 添加其他可能的legend事件处理
    chartInstance.value.on('legendselected', function(params) {
      try {
        if (!params) {
          console.warn('Invalid legend selected params:', params)
          return
        }
        console.log('Legend selected:', params)
      } catch (error) {
        console.warn('Legend selected error:', error)
      }
    })

    chartInstance.value.on('legendunselected', function(params) {
      try {
        if (!params) {
          console.warn('Invalid legend unselected params:', params)
          return
        }
        console.log('Legend unselected:', params)
      } catch (error) {
        console.warn('Legend unselected error:', error)
      }
    })

  } catch (error) {
    console.error('Chart rendering error:', error)
    message.error('图表渲染失败: ' + error.message)
  }
}

// 保存到历史
const saveToHistory = () => {
  if (!analysisResult.value || !excelData.value) return
  
  const historyItem = {
    id: Date.now(),
    fileName: fileList.value[0]?.name || '未知文件',
    date: new Date().toLocaleString('zh-CN'),
    type: getAnalysisTypeText(analysisType.value),
    result: analysisResult.value,
    dataSummary: {
      sheetName: excelData.value.sheetName,
      headers: excelData.value.headers,
      totalRows: excelData.value.totalRows
    }
  }
  
  analysisHistory.value.unshift(historyItem)
  if (analysisHistory.value.length > 50) {
    analysisHistory.value = analysisHistory.value.slice(0, 50)
  }
  
  message.success('已保存到历史记录')
}

// 生命周期增强
onMounted(() => {
  // 加载所有本地存储数据
  const savedApiKey = localStorage.getItem('deepseek_api_key')
  const savedHistory = localStorage.getItem('analysis_history')
  const savedTemplates = localStorage.getItem('custom_templates')
  
  if (savedApiKey) apiKey.value = savedApiKey
  if (savedHistory) analysisHistory.value = JSON.parse(savedHistory)
  if (savedTemplates) {
    const customTemplates = JSON.parse(savedTemplates)
    analysisTemplates.push(...customTemplates)
  }
})

// 监听变化
watch([apiKey, analysisHistory], () => {
  localStorage.setItem('deepseek_api_key', apiKey.value)
  localStorage.setItem('analysis_history', JSON.stringify(analysisHistory.value))
}, { deep: true })

// 监听图表类型变化，实现实时切换
watch(chartType, () => {
  if (chartData.value && showCharts.value) {
    renderChart()
  }
})

// 监听图表显示状态变化，实现动态渲染
watch(showCharts, (newVal) => {
  if (newVal && chartData.value) {
    nextTick(() => {
      renderChart()
    })
  }
})

// 提交分析
const handleSubmit = async () => {
  if (!excelData.value || !analysisRequirement.value.trim()) {
    message.warning('请上传文件并输入分析需求')
    return
  }

  if (!apiKey.value.trim()) {
    message.warning('请先配置DeepSeek API密钥')
    showSettings.value = true
    return
  }

  isAnalyzing.value = true
  errorMessage.value = ''
  progress.value = 0
  
  try {
    const progressInterval = setInterval(() => {
      if (progress.value < 90) {
        progress.value += 10
      }
    }, 200)

    const result = await callDeepSeekAPI()
    
    clearInterval(progressInterval)
    progress.value = 100
    
    analysisResult.value = result
    message.success('分析完成!')
    
    // 自动保存到历史
    saveToHistory()
  } catch (error) {
    errorMessage.value = error.message || '分析失败，请重试'
    console.error('API调用错误:', error)
  } finally {
    isAnalyzing.value = false
    setTimeout(() => {
      progress.value = 0
    }, 1000)
  }
}

// 真实的DeepSeek API调用
const callDeepSeekAPI = async () => {
  // 准备数据
  const dataSummary = {
    sheetName: excelData.value.sheetName,
    headers: excelData.value.headers,
    totalRows: excelData.value.totalRows,
    sampleData: excelData.value.rows.slice(0, 20).map(row => 
      excelData.value.headers.reduce((obj, header, index) => {
        obj[header] = row[index] || null
        return obj
      }, {})
    )
  }

  const prompt = `请作为数据分析师，分析以下Excel数据，根据用户需求提供专业分析报告。

## 分析要求
- 分析类型：${getAnalysisTypeText(analysisType.value)}
- 用户需求：${analysisRequirement.value}

## 数据结构
- 工作表：${dataSummary.sheetName}
- 列数：${dataSummary.headers.length}
- 行数：${dataSummary.totalRows}
- 列名：${dataSummary.headers.join(' | ')}

## 数据样本（前20行）
\`\`\`json
${JSON.stringify(dataSummary.sampleData, null, 2)}
\`\`\`

## 分析任务
请提供以下内容的详细分析：
1. 数据概览和基本统计信息
2. 针对用户具体需求的深度分析
3. 关键业务洞察和数据模式
4. 数据质量评估（缺失值、异常值等）
5. 基于分析结果的具体行动建议
6. 后续分析方向推荐

请使用清晰的Markdown格式，包含表格、列表和适当的格式化。`

  try {
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey.value.trim()}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的数据分析师，擅长Excel数据分析和商业洞察。请提供准确、实用的分析报告。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 3000,
        temperature: 0.7,
        stream: false
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`API调用失败: ${errorData.error?.message || response.statusText}`)
    }

    const data = await response.json()
    return data.choices[0]?.message?.content || '未能获取分析结果'
  } catch (error) {
    console.error('DeepSeek API调用错误:', error)
    throw error
  }
}

const getAnalysisTypeText = (type) => {
  const typeMap = {
    general: '通用分析',
    trend: '趋势分析',
    statistics: '统计分析',
    correlation: '相关性分析',
    anomaly: '异常检测'
  }
  return typeMap[type] || '通用分析'
}

// 下载结果
const downloadResult = () => {
  if (!analysisResult.value) return
  
  const blob = new Blob([analysisResult.value], { type: 'text/markdown' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `excel-analysis-${new Date().getTime()}.md`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  
  message.success('下载成功!')
}
</script>

<style scoped>
.analyse-doc-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
}

.analyse-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.feature-nav {
  margin-bottom: 24px;
}

.data-preview {
  margin: 16px 0;
}

.chart-container {
  height: 400px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .analyse-doc-container {
    padding: 12px;
  }
}
</style>
